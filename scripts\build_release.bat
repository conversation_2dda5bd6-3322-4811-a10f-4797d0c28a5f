@echo off
echo ========================================
echo Building BloodPlus for Production
echo ========================================

echo.
echo [1/5] Cleaning previous builds...
call flutter clean
if %errorlevel% neq 0 (
    echo ERROR: Flutter clean failed
    pause
    exit /b 1
)

echo.
echo [2/5] Getting dependencies...
call flutter pub get
if %errorlevel% neq 0 (
    echo ERROR: Flutter pub get failed
    pause
    exit /b 1
)

echo.
echo [3/5] Cleaning Android build...
cd android
call gradlew clean
if %errorlevel% neq 0 (
    echo ERROR: Gradle clean failed
    pause
    exit /b 1
)
cd ..

echo.
echo [4/5] Building release APK...
call flutter build apk --release
if %errorlevel% neq 0 (
    echo ERROR: Flutter build apk failed
    pause
    exit /b 1
)

echo.
echo [5/5] Building release App Bundle for Play Store...
call flutter build appbundle --release
if %errorlevel% neq 0 (
    echo ERROR: Flutter build appbundle failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build completed successfully!
echo ========================================
echo.
echo Files created:
echo - APK: build\app\outputs\flutter-apk\app-release.apk
echo - App Bundle: build\app\outputs\bundle\release\app-release.aab
echo.
echo The App Bundle (.aab) file is ready for Google Play Store upload.
echo.
pause
