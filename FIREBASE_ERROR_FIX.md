# 🔧 Khắc phục lỗi Firebase Authentication

## ❌ Lỗi hiện tại:
```
E/FirebaseAuth: This app is not authorized to use Firebase Authentication. 
Please verify that the correct package name, SHA-1, and SHA-256 are configured in the Firebase Console.
Invalid PlayIntegrity token; does not pass basic integrity.
```

## 📋 SHA-1 Fingerprint của bạn:
```
SHA1: B5:6E:96:EE:5C:C3:48:2A:45:C8:A3:EF:DC:89:5D:46:EF:A6:87:F1
SHA-256: 4C:83:9C:F1:D8:BC:F2:47:1F:8C:CF:BE:ED:14:FA:13:1E:48:C8:DF:90:98:0E:44:22:3C:CB:7D:B5:16:4F:F4
```

## 🔧 <PERSON><PERSON><PERSON> b<PERSON>ớ<PERSON> khắc phục:

### 1. **Cấu hình Firebase Console**

#### Bước 1: Thêm SHA-1 Fingerprint
1. <PERSON><PERSON><PERSON> cập: https://console.firebase.google.com/
2. Chọn project: **bloodplus-6be56**
3. Click vào ⚙️ **Project Settings**
4. Chọn tab **Your apps**
5. Tìm Android app: `com.j97.bloodplus`
6. Scroll xuống phần **SHA certificate fingerprints**
7. Click **Add fingerprint**
8. Paste SHA-1: `B5:6E:96:EE:5C:C3:48:2A:45:C8:A3:EF:DC:89:5D:46:EF:A6:87:F1`
9. Click **Save**

#### Bước 2: Kích hoạt Phone Authentication
1. Vào **Authentication** từ menu bên trái
2. Chọn tab **Sign-in method**
3. Tìm **Phone** trong danh sách providers
4. Click **Enable**
5. Đọc và đồng ý với Terms
6. Click **Save**

#### Bước 3: Thêm số điện thoại test (tùy chọn)
1. Trong **Phone** provider settings
2. Mở rộng **Phone numbers for testing**
3. Thêm số điện thoại test: `+***********`
4. Thêm verification code: `123456`
5. Click **Save**

### 2. **Cập nhật google-services.json**
1. Trong **Project Settings** > **Your apps**
2. Click **Download google-services.json**
3. Thay thế file trong `android/app/google-services.json`

### 3. **Clean và Rebuild**
```bash
flutter clean
flutter pub get
cd android
./gradlew clean
cd ..
flutter build apk --debug
```

### 4. **Test trên thiết bị thật**
- Lỗi PlayIntegrity thường xảy ra trên emulator
- Test trên thiết bị Android thật để có kết quả tốt nhất
- Đảm bảo thiết bị có Google Play Services

## 🚨 **Lưu ý quan trọng:**

### PlayIntegrity Issues:
- **Emulator**: Thường gặp lỗi PlayIntegrity
- **Thiết bị thật**: Hoạt động tốt hơn
- **Debug build**: Có thể gặp vấn đề với Play Integrity

### Alternative Solutions:
1. **Test với số điện thoại test** (không cần SMS thật)
2. **Sử dụng thiết bị thật** thay vì emulator
3. **Kiểm tra Google Play Services** trên thiết bị

## 🔍 **Debug Steps:**

### 1. Kiểm tra Firebase Project ID
Trong `android/app/google-services.json`:
```json
{
  "project_info": {
    "project_id": "bloodplus-6be56"
  }
}
```

### 2. Kiểm tra Package Name
Trong `android/app/build.gradle.kts`:
```kotlin
applicationId = "com.j97.bloodplus"
```

### 3. Kiểm tra Console Logs
```bash
flutter run --verbose
```
Tìm các log:
- `Firebase initialized successfully`
- `Firebase Auth Error:`
- `VERIFY PHONE RESPONSE`

## 📱 **Test Flow sau khi fix:**

1. **Mở app** → Login screen
2. **Click "Đăng ký"** → Check Information screen
3. **Nhập email + phone** → Click "Tiếp tục"
4. **Nhận OTP** (hoặc dùng test code `123456`)
5. **Nhập OTP** → Phone Verification screen
6. **Xác thực thành công** → Registration screen
7. **Điền form** → Create account
8. **Auto login** → Home screen

## 🆘 **Nếu vẫn lỗi:**

### Option 1: Sử dụng số test
- Phone: `+***********`
- OTP: `123456`

### Option 2: Tạo project Firebase mới
1. Tạo project mới trên Firebase Console
2. Thêm Android app với package name: `com.j97.bloodplus`
3. Thêm SHA-1 fingerprint
4. Download google-services.json mới
5. Enable Phone Authentication

### Option 3: Bypass Firebase (development only)
- Tạm thời comment Firebase initialization
- Mock OTP verification
- Test UI flow trước

## 📞 **Contact Support:**
Nếu vẫn gặp vấn đề, cung cấp:
1. Console logs đầy đủ
2. Screenshot Firebase Console settings
3. Device/emulator information
4. google-services.json content (remove sensitive data)
