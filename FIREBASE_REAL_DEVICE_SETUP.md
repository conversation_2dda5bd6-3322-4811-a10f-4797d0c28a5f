# 🔥 Firebase Setup cho Thiết bị Thật

## 📋 **Trạng thái hiện tại**
- ✅ Code đã được cập nhật để sử dụng Firebase thật
- ✅ Error handling được cải thiện
- ✅ Logging chi tiết để debug
- ✅ Phone number formatting tự động

## 🔧 **Cần thiết lập trong Firebase Console**

### 1. **Thêm SHA-1 Fingerprints**
Bạn cần thêm cả debug và release SHA-1:

**Debug SHA-1** (đã có):
```
B5:6E:96:EE:5C:C3:48:2A:45:C8:A3:EF:DC:89:5D:46:EF:A6:87:F1
```

**Release SHA-1** (mới):
```
A5:81:F5:EE:6A:4F:06:DC:6C:DF:03:8B:8A:8C:36:E5:87:21:42:96
```

#### Cách thêm:
1. Vào [Firebase Console](https://console.firebase.google.com/)
2. <PERSON>ọn project `bloodplus-6be56`
3. ⚙️ **Project Settings** > **Your apps** > Android app
4. Scroll xuống **SHA certificate fingerprints**
5. **Add fingerprint** và paste cả 2 SHA-1 trên
6. **Save**

### 2. **Enable Phone Authentication**
1. **Authentication** > **Sign-in method**
2. Tìm **Phone** provider
3. **Enable**
4. Đọc và đồng ý Terms of Service
5. **Save**

### 3. **Cấu hình App Check (Tùy chọn)**
1. **App Check** từ menu bên trái
2. **Register app** nếu chưa có
3. Chọn **Play Integrity** provider
4. **Save**

## 📱 **Test trên Thiết bị Thật**

### **Bước 1: Build và Install**
```bash
# Sử dụng script có sẵn
scripts\test_firebase_real_device.bat

# Hoặc manual
flutter clean
flutter pub get
flutter build apk --debug
flutter install
```

### **Bước 2: Test Flow**
1. **Mở app** trên thiết bị
2. **Login screen** → Click "Đăng ký"
3. **Check Information screen**:
   - Nhập email hợp lệ
   - Nhập số điện thoại (VD: **********)
   - Click "Tiếp tục"
4. **Chờ SMS** (có thể mất 1-2 phút)
5. **Phone Verification screen**:
   - Nhập mã OTP 6 số từ SMS
   - Click "Xác thực"
6. **Registration screen** → Điền form và đăng ký

### **Bước 3: Monitor Logs**
Mở terminal khác và chạy:
```bash
flutter logs
```

Tìm các log quan trọng:
- `=== PHONE NUMBER FORMATTING ===`
- `=== FIREBASE AUTH ERROR ===`
- `=== OTP VERIFICATION ===`
- `=== VERIFICATION SUCCESS ===`

## 🐛 **Troubleshooting**

### **Lỗi thường gặp:**

#### 1. **"App not authorized"**
```
Lỗi: This app is not authorized to use Firebase Authentication
```
**Giải pháp:**
- Kiểm tra SHA-1 fingerprint đã thêm đúng chưa
- Download google-services.json mới từ Firebase Console
- Thay thế file cũ trong `android/app/`

#### 2. **"Play Integrity failed"**
```
Lỗi: Invalid PlayIntegrity token
```
**Giải pháp:**
- Đảm bảo thiết bị có Google Play Services
- Không test trên emulator
- Thử trên thiết bị Android thật

#### 3. **"Too many requests"**
```
Lỗi: Too many requests. Please try again later
```
**Giải pháp:**
- Chờ 1-2 phút rồi thử lại
- Firebase có rate limiting cho SMS
- Sử dụng số điện thoại test nếu cần

#### 4. **"Invalid phone number"**
```
Lỗi: The phone number provided is invalid
```
**Giải pháp:**
- Đảm bảo số điện thoại đúng định dạng
- VD: ********** → +84337252208
- Code đã tự động format

### **Debug Steps:**

#### 1. **Kiểm tra Firebase Project**
```bash
# Trong android/app/google-services.json
"project_id": "bloodplus-6be56"
"package_name": "com.j97.bloodplus"
```

#### 2. **Kiểm tra Device**
- Google Play Services đã cài đặt
- Kết nối internet ổn định
- Không phải emulator

#### 3. **Kiểm tra Console Logs**
```bash
flutter logs | grep -E "(Firebase|OTP|Phone|Error)"
```

## 📞 **Test với số điện thoại thật**

### **Lưu ý quan trọng:**
- Firebase có giới hạn SMS miễn phí (khoảng 10-50 SMS/ngày)
- Mỗi số điện thoại có thể có rate limiting
- SMS có thể mất 1-2 phút để đến

### **Định dạng số điện thoại hỗ trợ:**
- `**********` → `+84337252208`
- `84337252208` → `+84337252208`
- `+84337252208` → `+84337252208`

### **Nếu không nhận được SMS:**
1. Kiểm tra spam/junk folder
2. Thử số điện thoại khác
3. Chờ 2-3 phút
4. Kiểm tra Firebase Console > Authentication > Users

## 🎯 **Expected Results**

### **Thành công:**
- SMS OTP được gửi đến điện thoại
- Nhập OTP → Xác thực thành công
- Chuyển đến Registration screen
- Tạo tài khoản thành công

### **Logs thành công:**
```
=== PHONE NUMBER FORMATTING ===
Original: **********
Formatted: +84337252208
==============================

=== OTP VERIFICATION ===
Verification ID: AMkJKxxx...
OTP Code: 123456
======================

=== VERIFICATION SUCCESS ===
User UID: abc123...
Phone Number: +84337252208
ID Token: eyJhbGciOiJSUzI1NiI...
===========================
```

## 🆘 **Nếu vẫn gặp vấn đề**

1. **Kiểm tra Firebase Console**:
   - Authentication > Users (có user mới không?)
   - Authentication > Settings > Authorized domains

2. **Thử số điện thoại test**:
   - Vào Firebase Console > Authentication > Sign-in method > Phone
   - Thêm test phone number: `+84337252208` với code: `123456`

3. **Contact Support**:
   - Cung cấp full logs
   - Screenshot Firebase Console settings
   - Device information

---
**🔥 Chúc bạn test thành công với Firebase trên thiết bị thật! 🔥**
