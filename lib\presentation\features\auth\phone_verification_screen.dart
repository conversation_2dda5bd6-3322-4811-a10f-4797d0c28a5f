import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:pinput/pinput.dart';
import 'package:bloodplus/core/constants/app_colors.dart';
import 'package:bloodplus/core/widgets/custom_button.dart';
import 'package:bloodplus/data/services/phone_auth_service.dart';
import 'package:bloodplus/data/services/auth_service.dart';
import 'package:bloodplus/data/manager/app_state_notifier.dart';
import 'package:bloodplus/presentation/features/auth/registration_screen.dart';
import 'package:provider/provider.dart';

class PhoneVerificationScreen extends StatefulWidget {
  final String phoneNumber;
  final PhoneAuthService? phoneAuthService;

  const PhoneVerificationScreen({
    Key? key,
    required this.phoneNumber,
    this.phoneAuthService,
  }) : super(key: key);

  @override
  _PhoneVerificationScreenState createState() => _PhoneVerificationScreenState();
}

class _PhoneVerificationScreenState extends State<PhoneVerificationScreen> {
  final TextEditingController _otpController = TextEditingController();
  late final PhoneAuthService _phoneAuthService;
  late final AuthService _authService;
  bool _isLoading = false;
  bool _isResending = false;
  int _resendCountdown = 0;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    // Sử dụng instance được truyền vào hoặc tạo mới
    _phoneAuthService = widget.phoneAuthService ?? PhoneAuthService();

    print('=== PHONE VERIFICATION SCREEN INIT ===');
    print('Using passed service: ${widget.phoneAuthService != null}');
    print('Service instance: ${_phoneAuthService.hashCode}');
    print('Initial verification status: ${_phoneAuthService.getVerificationStatus()}');
    print('=====================================');

    _startResendCountdown();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final appStateNotifier = Provider.of<AppStateNotifier>(context, listen: false);
    _authService = AuthService(appStateNotifier: appStateNotifier);
  }

  void _startResendCountdown() {
    setState(() {
      _resendCountdown = 60;
    });
    
    Future.doWhile(() async {
      await Future.delayed(const Duration(seconds: 1));
      if (mounted) {
        setState(() {
          _resendCountdown--;
        });
        return _resendCountdown > 0;
      }
      return false;
    });
  }

  Future<void> _verifyOTP() async {
    if (_otpController.text.length != 6) {
      setState(() {
        _errorMessage = 'Vui lòng nhập đầy đủ 6 số';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Bước 1: Xác thực OTP với Firebase
      final firebaseResult = await _phoneAuthService.verifyOTP(
        otp: _otpController.text,
      );

      if (firebaseResult['success'] == true) {
        final idToken = firebaseResult['idToken'];

        // Bước 2: Gọi API backend để verify phone number
        final backendResult = await _authService.verifyPhoneNumber(
          idToken: idToken,
          phoneNumber: widget.phoneNumber,
        );

        if (backendResult['success'] == true) {
          // Xác thực thành công, chuyển đến màn hình đăng ký
          if (mounted) {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(
                builder: (context) => RegistrationScreen(
                  phoneNumber: widget.phoneNumber,
                  idToken: idToken,
                ),
              ),
            );
          }
        } else {
          setState(() {
            _errorMessage = backendResult['error'] ?? 'Số điện thoại đã được sử dụng';
          });
        }
      } else {
        setState(() {
          _errorMessage = firebaseResult['error'] ?? 'Xác thực OTP thất bại';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Lỗi xác thực: ${e.toString()}';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _resendOTP() async {
    if (_resendCountdown > 0) return;

    setState(() {
      _isResending = true;
      _errorMessage = null;
    });

    try {
      final result = await _phoneAuthService.resendOTP(
        phoneNumber: widget.phoneNumber,
        onCodeSent: (message) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(message),
                backgroundColor: Colors.green,
              ),
            );
            _startResendCountdown();
          }
        },
        onError: (error) {
          if (mounted) {
            setState(() {
              _errorMessage = error;
            });
          }
        },
      );

      if (result['success'] != true) {
        setState(() {
          _errorMessage = result['error'] ?? 'Gửi lại OTP thất bại';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Lỗi gửi OTP: ${e.toString()}';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isResending = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _otpController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final defaultPinTheme = PinTheme(
      width: 56,
      height: 56,
      textStyle: const TextStyle(
        fontSize: 20,
        color: Color.fromRGBO(30, 60, 87, 1),
        fontWeight: FontWeight.w600,
      ),
      decoration: BoxDecoration(
        border: Border.all(color: const Color.fromRGBO(234, 239, 243, 1)),
        borderRadius: BorderRadius.circular(10),
      ),
    );

    final focusedPinTheme = defaultPinTheme.copyDecorationWith(
      border: Border.all(color: AppColors.primaryRed),
      borderRadius: BorderRadius.circular(8),
    );

    final submittedPinTheme = defaultPinTheme.copyWith(
      decoration: defaultPinTheme.decoration?.copyWith(
        color: const Color.fromRGBO(234, 239, 243, 1),
      ),
    );

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Xác thực số điện thoại',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(height: 40),
              
              // Icon
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: AppColors.primaryRed.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(40),
                ),
                child: const Icon(
                  Icons.phone_android,
                  size: 40,
                  color: AppColors.primaryRed,
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Title
              const Text(
                'Nhập mã xác thực',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 16),
              
              // Description
              Text(
                'Chúng tôi đã gửi mã xác thực 6 số đến\n${widget.phoneNumber}',
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 40),
              
              // OTP Input
              Pinput(
                controller: _otpController,
                length: 6,
                defaultPinTheme: defaultPinTheme,
                focusedPinTheme: focusedPinTheme,
                submittedPinTheme: submittedPinTheme,
                validator: (s) {
                  return s == null || s.length == 6 ? null : 'Vui lòng nhập đầy đủ 6 số';
                },
                pinputAutovalidateMode: PinputAutovalidateMode.onSubmit,
                showCursor: true,
                onCompleted: (pin) => _verifyOTP(),
              ),
              
              const SizedBox(height: 20),
              
              // Error message
              if (_errorMessage != null)
                Container(
                  padding: const EdgeInsets.all(16),
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.red.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          _errorMessage!,
                          style: const TextStyle(
                            color: Colors.red,
                            fontSize: 14,
                            height: 1.4,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              
              const SizedBox(height: 40),
              
              // Verify Button
              SizedBox(
                width: double.infinity,
                child: CustomButton(
                  text: _isLoading ? 'Đang xác thực...' : 'Xác thực',
                  color: AppColors.primaryRed,
                  onPressed: _isLoading ? null : _verifyOTP,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  borderRadius: 10,
                ),
              ),
              
              const SizedBox(height: 20),

              // Debug button (only in debug mode)
              if (kDebugMode) ...[
                TextButton(
                  onPressed: () {
                    final status = _phoneAuthService.getVerificationStatus();
                    showDialog(
                      context: context,
                      builder: (context) => AlertDialog(
                        title: const Text('Debug: Verification Status'),
                        content: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Has Verification ID: ${status['hasVerificationId']}'),
                            Text('Verification ID: ${status['verificationId']}'),
                            Text('ID Length: ${status['verificationIdLength']}'),
                            Text('Resend Token: ${status['resendToken']}'),
                            Text('Development Mode: ${status['isDevelopmentMode']}'),
                          ],
                        ),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.pop(context),
                            child: const Text('OK'),
                          ),
                        ],
                      ),
                    );
                  },
                  child: const Text(
                    'DEBUG: Check Status',
                    style: TextStyle(color: Colors.orange, fontSize: 12),
                  ),
                ),
                const SizedBox(height: 10),
              ],

              // Resend OTP
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text(
                    'Không nhận được mã? ',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                  TextButton(
                    onPressed: _resendCountdown > 0 || _isResending ? null : _resendOTP,
                    child: Text(
                      _resendCountdown > 0
                          ? 'Gửi lại (${_resendCountdown}s)'
                          : _isResending
                              ? 'Đang gửi...'
                              : 'Gửi lại',
                      style: TextStyle(
                        fontSize: 14,
                        color: _resendCountdown > 0 || _isResending
                            ? Colors.grey
                            : AppColors.primaryRed,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              
              const Spacer(),
            ],
          ),
        ),
      ),
    );
  }
}
