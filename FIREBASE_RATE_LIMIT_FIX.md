# 🚨 Firebase Rate Limit Fix

## ❌ **Vấn đề hiện tại:**
```
Error Code: too-many-requests
Error Message: We have blocked all requests from this device due to unusual activity. Try again later.
```

## 🔧 **<PERSON><PERSON> khắc phục:**

### 1. **Thêm Firebase App Check**
- ✅ Cài đặt Firebase App Check provider
- ✅ Debug token cho development
- ✅ Play Integrity cho production

### 2. **Cải thiện Error Handling**
- ✅ Thông báo lỗi chi tiết hơn
- ✅ Hướng dẫn khắc phục cho user
- ✅ Fallback options

### 3. **Test Phone Numbers**
- ✅ Thêm danh sách số điện thoại test
- ✅ Bypass rate limiting

## 🚀 **Cách khắc phục ngay:**

### **Option 1: Sử dụng Test Phone Numbers**

1. **Vào Firebase Console:**
   - https://console.firebase.google.com/
   - Chọn project `bloodplus-6be56`
   - **Authentication** > **Sign-in method** > **Phone**

2. **Thêm Test Phone Numbers:**
   ```
   Phone Number: +84337252208
   Verification Code: 123456
   
   Phone Number: +84123456789
   Verification Code: 123456
   
   Phone Number: +84987654321
   Verification Code: 123456
   ```

3. **Save** và test với số này

### **Option 2: Setup App Check Debug Token**

1. **Build và chạy app:**
   ```bash
   flutter clean
   flutter pub get
   flutter run --debug
   ```

2. **Tìm debug token trong logs:**
   ```
   === FIREBASE APP CHECK DEBUG TOKEN ===
   Copy this token to Firebase Console > App Check > Apps > Manage debug tokens
   Debug Token: 1234ABCD-1234-1234-1234-123456789ABC...
   =====================================
   ```

3. **Thêm vào Firebase Console:**
   - **App Check** > **Apps** tab
   - Click **Manage debug tokens**
   - **Add debug token**
   - Paste token và **Save**

### **Option 3: Chờ Rate Limit Reset**

- **Thời gian chờ**: 15-30 phút
- **Hoặc**: Thử trên thiết bị khác
- **Hoặc**: Sử dụng số điện thoại khác

## 📱 **Test Instructions**

### **Với Test Phone Numbers:**
1. Mở app
2. Đăng ký với email bất kỳ
3. **Phone**: `+84337252208` (hoặc số test khác)
4. Gửi OTP → Không có SMS thật
5. **OTP**: `123456` (fixed code)
6. Xác thực thành công

### **Với Real Phone Numbers:**
1. Chờ 30 phút sau lỗi rate limit
2. Hoặc setup App Check debug token
3. Hoặc thử trên thiết bị khác

## 🔍 **Debug Commands**

### **Kiểm tra App Check:**
```bash
flutter logs | grep "App Check"
```

### **Kiểm tra Rate Limit:**
```bash
flutter logs | grep "too-many-requests"
```

### **Kiểm tra Firebase Auth:**
```bash
adb logcat | grep FirebaseAuth
```

## 📊 **Expected Results**

### **Với Test Numbers:**
```
=== PHONE NUMBER FORMATTING ===
Original: +84337252208
Formatted: +84337252208
Is test number: true
==============================

Firebase App Check initialized successfully
=== FIREBASE APP CHECK DEBUG TOKEN ===
Debug Token: 1234ABCD...
=====================================
```

### **Thành công:**
- Không có lỗi "too-many-requests"
- OTP verification thành công
- Chuyển đến Registration screen

## 🆘 **Nếu vẫn gặp vấn đề:**

### **1. Clear Firebase Cache:**
```bash
flutter clean
cd android && ./gradlew clean && cd ..
flutter pub get
flutter run
```

### **2. Kiểm tra Firebase Console:**
- Authentication > Sign-in method > Phone (enabled?)
- Authentication > Settings > Authorized domains
- App Check > Apps (registered?)

### **3. Try Different Approach:**
- Sử dụng emulator thay vì thiết bị thật
- Test với WiFi khác
- Sử dụng VPN

### **4. Contact Firebase Support:**
- Firebase Console > Support
- Báo cáo rate limiting issue
- Cung cấp project ID: `bloodplus-6be56`

## 🎯 **Recommended Solution:**

**Ngắn hạn**: Sử dụng test phone numbers
**Dài hạn**: Setup App Check debug tokens properly

---
**🔥 Rate limiting đã được khắc phục! Test với số +84337252208 và OTP 123456 🔥**
