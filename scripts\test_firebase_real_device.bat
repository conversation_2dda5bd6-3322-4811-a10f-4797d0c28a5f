@echo off
echo ========================================
echo Testing Firebase on Real Device
echo ========================================

echo.
echo [1/5] Cleaning previous builds...
call flutter clean
if %errorlevel% neq 0 (
    echo ERROR: Flutter clean failed
    pause
    exit /b 1
)

echo.
echo [2/5] Getting dependencies...
call flutter pub get
if %errorlevel% neq 0 (
    echo ERROR: Flutter pub get failed
    pause
    exit /b 1
)

echo.
echo [3/5] Cleaning Android build...
cd android
call gradlew clean
if %errorlevel% neq 0 (
    echo ERROR: Gradle clean failed
    pause
    exit /b 1
)
cd ..

echo.
echo [4/5] Building debug APK with Firebase enabled...
call flutter build apk --debug
if %errorlevel% neq 0 (
    echo ERROR: Flutter build apk failed
    pause
    exit /b 1
)

echo.
echo [5/5] Installing on connected device...
call flutter install
if %errorlevel% neq 0 (
    echo ERROR: Flutter install failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo Firebase Test Build Complete!
echo ========================================
echo.
echo IMPORTANT: 
echo - Make sure your device has Google Play Services
echo - Use a real phone number for testing
echo - Check Firebase Console for any errors
echo - Monitor logs with: flutter logs
echo.
echo Test Steps:
echo 1. Open the app on your device
echo 2. Go to Login screen
echo 3. Click "Dang ky" (Register)
echo 4. Enter email and phone number
echo 5. Click "Tiep tuc" to send OTP
echo 6. Check your phone for SMS
echo 7. Enter the 6-digit OTP code
echo.
pause
