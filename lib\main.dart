import 'package:bloodplus/core/language_helper/language_manager.dart';
import 'package:bloodplus/data/manager/app_state_notifier.dart';
import 'package:bloodplus/presentation/features/onboarding/splash/splash_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:flutter/foundation.dart';
import 'core/language_helper/localization.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    await Firebase.initializeApp();
    print('Firebase initialized successfully');

    // Initialize Firebase App Check
    await FirebaseAppCheck.instance.activate(
      // Use debug provider for development
      androidProvider: kDebugMode
          ? AndroidProvider.debug
          : AndroidProvider.playIntegrity,
    );
    print('Firebase App Check initialized successfully');

    // Get debug token for development
    if (kDebugMode) {
      try {
        final token = await FirebaseAppCheck.instance.getToken();
        print('=== FIREBASE APP CHECK DEBUG TOKEN ===');
        print('Copy this token to Firebase Console > App Check > Apps > Manage debug tokens');
        print('Debug Token: ${token?.substring(0, 50)}...');
        print('=====================================');
      } catch (e) {
        print('Failed to get App Check debug token: $e');
      }
    }
  } catch (e) {
    print('Firebase initialization error: $e');
    // Tiếp tục chạy app ngay cả khi Firebase có lỗi
  }

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => LanguageManager()..loadLanguage()),
        ChangeNotifierProvider(create: (context) => AppStateNotifier()),
      ],
      child: const BloodDonationApp(),
    ),
  );
}

class BloodDonationApp extends StatelessWidget {
  const BloodDonationApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<LanguageManager>(
      builder: (context, languageManager, child) {
        return MaterialApp(
          title: 'Mau Plus',
          theme: ThemeData(
            primarySwatch: Colors.red,
          ),
          home: const SplashScreen(),
          debugShowCheckedModeBanner: false,
          locale: languageManager.locale,
          supportedLocales: AppLocalizations.supportedLocales,
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
        );
      },
    );
  }
}