# 🚀 Hướng dẫn Deploy BloodPlus lên Google Play Store

## 📋 **Thông tin quan trọng**

### 🔑 **Keystore Information**
- **File**: `android/app/bloodplus-release-key.keystore`
- **Alias**: `bloodplus`
- **Store Password**: `bloodplus123`
- **Key Password**: `bloodplus123`
- **SHA-1 Fingerprint**: `A5:81:F5:EE:6A:4F:06:DC:6C:DF:03:8B:8A:8C:36:E5:87:21:42:96`

### 🏗️ **Build Configuration**
- **Debug Mode**: Sử dụng mock OTP (123456), disable Play Integrity
- **Release Mode**: Sử dụng Firebase thật, enable Play Integrity

## 🔧 **Chu<PERSON>n bị trước khi deploy**

### 1. **Cấu hình Firebase Console**
1. <PERSON><PERSON><PERSON> cập [Firebase Console](https://console.firebase.google.com/)
2. Chọn project `bloodplus-6be56`
3. Vào **Project Settings** > **Your apps** > **Android app**
4. **Thêm SHA-1 fingerprint cho production**:
   ```
   A5:81:F5:EE:6A:4F:06:DC:6C:DF:03:8B:8A:8C:36:E5:87:21:42:96
   ```
5. **Download google-services.json mới** và thay thế file cũ
6. Vào **Authentication** > **Sign-in method** > **Phone** > Enable

### 2. **Cấu hình App Check (nếu cần)**
1. Vào **App Check** trong Firebase Console
2. Chọn **Play Integrity** provider
3. Nếu gặp lỗi, thêm debug token cho development

## 🏗️ **Build Commands**

### **Debug Build (Development)**
```bash
# Sử dụng script có sẵn
scripts\build_debug.bat

# Hoặc manual
flutter clean
flutter pub get
flutter build apk --debug
```

### **Release Build (Production)**
```bash
# Sử dụng script có sẵn
scripts\build_release.bat

# Hoặc manual
flutter clean
flutter pub get
flutter build appbundle --release
```

## 📦 **Files được tạo**

### **Debug Build**
- `build/app/outputs/flutter-apk/app-debug.apk`
- Có mock OTP, disable Play Integrity
- Dùng để test trên thiết bị

### **Release Build**
- `build/app/outputs/flutter-apk/app-release.apk` (APK file)
- `build/app/outputs/bundle/release/app-release.aab` (App Bundle - dùng cho Play Store)
- Sử dụng Firebase thật, enable Play Integrity

## 🚀 **Deploy lên Google Play Store**

### **✅ Build đã sẵn sàng!**
- **APK**: `build/app/outputs/flutter-apk/app-release.apk` (63.1MB)
- **App Bundle**: `build/app/outputs/bundle/release/app-release.aab` (33.2MB)

### **Bước 1: Chuẩn bị**
1. Tạo tài khoản Google Play Console ($25 one-time fee)
2. Tạo app mới với package name: `com.j97.bloodplus`
3. Upload app icon và screenshots

### **Bước 2: Upload App Bundle**
1. Chạy `scripts\build_release.bat` (hoặc đã có file sẵn)
2. Upload file `build/app/outputs/bundle/release/app-release.aab`
3. Điền thông tin app (mô tả, screenshots, etc.)

### **Bước 3: Testing**
1. Upload lên Internal Testing track trước
2. Test trên thiết bị thật
3. Kiểm tra Firebase Authentication hoạt động

### **Bước 4: Production**
1. Chuyển từ Internal Testing sang Production
2. Submit for review

## 🔧 **Troubleshooting**

### **Lỗi Play Integrity**
- Đảm bảo SHA-1 fingerprint đã được thêm vào Firebase
- Test trên thiết bị thật (không phải emulator)
- Thêm debug token nếu cần

### **Lỗi Firebase Authentication**
- Kiểm tra google-services.json đã update
- Đảm bảo Phone Authentication đã enable
- Kiểm tra quota SMS

### **Lỗi Build**
- Chạy `flutter clean` và `flutter pub get`
- Kiểm tra keystore file tồn tại
- Đảm bảo key.properties đúng format

## 📱 **Test trước khi deploy**

### **Debug Mode Test**
1. Build debug APK
2. Install trên thiết bị
3. Test với OTP mock (123456)
4. Kiểm tra tất cả features

### **Release Mode Test**
1. Build release APK
2. Install trên thiết bị
3. Test với Firebase thật
4. Kiểm tra performance

## 🔐 **Bảo mật**

### **Keystore Security**
- **QUAN TRỌNG**: Backup keystore file an toàn
- Không commit keystore vào git
- Lưu password ở nơi an toàn

### **Firebase Security**
- Cấu hình App Check cho production
- Giới hạn API usage
- Monitor authentication logs

## 📞 **Liên hệ hỗ trợ**
- Nếu gặp vấn đề, kiểm tra logs trong Firebase Console
- Test trên nhiều thiết bị khác nhau
- Đảm bảo internet connection ổn định khi test
