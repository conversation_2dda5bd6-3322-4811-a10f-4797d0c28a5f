<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- The INTERNET permission is required for development. Specifically,
         the Flutter tool needs it to communicate with the running application
         to allow setting breakpoints, to provide hot reload, etc.
    -->
    <uses-permission android:name="android.permission.INTERNET"/>

    <application>
        <!-- Disable Play Integrity for Firebase Auth in debug builds only -->
        <meta-data
            android:name="firebase_auth_disable_play_integrity"
            android:value="true" />
    </application>
</manifest>
