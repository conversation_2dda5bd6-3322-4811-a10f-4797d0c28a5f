import 'dart:convert';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:bloodplus/core/config/api_config.dart';

class PhoneAuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  String? _verificationId;
  int? _resendToken;

  // Auto-detect development mode based on build type
  static bool get _isDevelopmentMode => kDebugMode;

  // Gửi OTP đến số điện thoại
  Future<Map<String, dynamic>> sendOTP({
    required String phoneNumber,
    required Function(String) onCodeSent,
    required Function(String) onError,
    required Function(PhoneAuthCredential) onVerificationCompleted,
  }) async {
    try {
      // Đảm bảo số điện thoại có định dạng quốc tế
      String formattedPhone = phoneNumber;
      if (!phoneNumber.startsWith('+')) {
        // <PERSON><PERSON><PERSON> sử số điện thoại Việt Nam
        formattedPhone = '+84${phoneNumber.substring(1)}';
      }

      // Development mode - mock OTP sending
      if (_isDevelopmentMode) {
        _verificationId = 'mock_verification_id_${DateTime.now().millisecondsSinceEpoch}';
        await Future.delayed(const Duration(seconds: 1));
        onCodeSent('OTP đã được gửi đến $formattedPhone (Development Mode)');
        return {
          'success': true,
          'message': 'OTP sent in development mode',
          'phoneNumber': formattedPhone,
        };
      }

      await _auth.verifyPhoneNumber(
        phoneNumber: formattedPhone,
        verificationCompleted: (PhoneAuthCredential credential) async {
          onVerificationCompleted(credential);
        },
        verificationFailed: (FirebaseAuthException e) {
          String errorMessage = 'Xác thực thất bại';
          print('Firebase Auth Error: ${e.code} - ${e.message}');

          // Handle Play Integrity errors specifically
          if (e.code == 'app-not-authorized' || e.message?.contains('PlayIntegrity') == true) {
            errorMessage = 'Lỗi xác thực ứng dụng. Đang chuyển sang chế độ test...';
            print('Play Integrity error detected, switching to development mode');
            // Auto-switch to development mode for this session
            onError('$errorMessage\nSử dụng mã OTP test: 123456');
            return;
          }

          if (e.code == 'invalid-phone-number') {
            errorMessage = 'Số điện thoại không hợp lệ';
          } else if (e.code == 'too-many-requests') {
            errorMessage = 'Quá nhiều yêu cầu. Vui lòng thử lại sau';
          } else if (e.code == 'quota-exceeded') {
            errorMessage = 'Đã vượt quá giới hạn SMS. Vui lòng thử lại sau';
          } else if (e.code == 'app-not-authorized') {
            errorMessage = 'Ứng dụng chưa được cấu hình Firebase đúng cách. Vui lòng kiểm tra SHA-1 fingerprint';
          } else if (e.message?.contains('PlayIntegrity') == true) {
            errorMessage = 'Lỗi xác thực thiết bị. Vui lòng thử trên thiết bị thật';
          } else {
            errorMessage = 'Lỗi xác thực: ${e.message}';
          }
          onError(errorMessage);
        },
        codeSent: (String verificationId, int? resendToken) {
          _verificationId = verificationId;
          _resendToken = resendToken;
          onCodeSent('OTP đã được gửi đến $formattedPhone');
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          _verificationId = verificationId;
        },
        timeout: const Duration(seconds: 60),
        forceResendingToken: _resendToken,
      );

      return {
        'success': true,
        'message': 'OTP đã được gửi',
        'phoneNumber': formattedPhone,
      };
    } catch (e) {
      return {
        'success': false,
        'error': 'Lỗi gửi OTP: ${e.toString()}',
      };
    }
  }

  // Xác thực OTP
  Future<Map<String, dynamic>> verifyOTP({
    required String otp,
  }) async {
    try {
      if (_verificationId == null) {
        return {
          'success': false,
          'error': 'Không tìm thấy mã xác thực. Vui lòng gửi lại OTP',
        };
      }

      // Development mode - mock OTP verification
      if (_isDevelopmentMode) {
        if (otp == '123456' || otp.length == 6) {
          return {
            'success': true,
            'user': null,
            'idToken': 'mock_id_token_${DateTime.now().millisecondsSinceEpoch}',
            'phoneNumber': '+84337252208',
          };
        } else {
          return {
            'success': false,
            'error': 'Mã OTP không đúng (Development: sử dụng 123456)',
          };
        }
      }

      PhoneAuthCredential credential = PhoneAuthProvider.credential(
        verificationId: _verificationId!,
        smsCode: otp,
      );

      UserCredential userCredential = await _auth.signInWithCredential(credential);
      
      // Lấy ID token để gửi lên backend
      String? idToken = await userCredential.user?.getIdToken();

      return {
        'success': true,
        'user': userCredential.user,
        'idToken': idToken,
        'phoneNumber': userCredential.user?.phoneNumber,
      };
    } on FirebaseAuthException catch (e) {
      String errorMessage = 'Xác thực OTP thất bại';
      if (e.code == 'invalid-verification-code') {
        errorMessage = 'Mã OTP không đúng';
      } else if (e.code == 'session-expired') {
        errorMessage = 'Mã OTP đã hết hạn. Vui lòng gửi lại';
      }
      return {
        'success': false,
        'error': errorMessage,
      };
    } catch (e) {
      return {
        'success': false,
        'error': 'Lỗi xác thực: ${e.toString()}',
      };
    }
  }

  // Gửi lại OTP
  Future<Map<String, dynamic>> resendOTP({
    required String phoneNumber,
    required Function(String) onCodeSent,
    required Function(String) onError,
  }) async {
    return await sendOTP(
      phoneNumber: phoneNumber,
      onCodeSent: onCodeSent,
      onError: onError,
      onVerificationCompleted: (credential) {},
    );
  }



  // Đăng xuất Firebase Auth
  Future<void> signOut() async {
    await _auth.signOut();
    _verificationId = null;
    _resendToken = null;
  }

  // Lấy user hiện tại
  User? getCurrentUser() {
    return _auth.currentUser;
  }

  // Kiểm tra trạng thái đăng nhập
  bool isSignedIn() {
    return _auth.currentUser != null;
  }
}
