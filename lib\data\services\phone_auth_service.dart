import 'dart:convert';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:bloodplus/core/config/api_config.dart';

class PhoneAuthService {
  // Singleton pattern
  static final PhoneAuthService _instance = PhoneAuthService._internal();
  factory PhoneAuthService() => _instance;
  PhoneAuthService._internal();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  String? _verificationId;
  int? _resendToken;

  // Auto-detect development mode based on build type
  // Set to false to always use Firebase for testing on real devices
  static bool get _isDevelopmentMode => false; // Use real Firebase with test numbers

  // Test phone numbers to bypass rate limiting
  static const List<String> _testPhoneNumbers = [
    '+84337252208',
    '+84123456789',
    '+84987654321',
  ];

  // Gửi OTP đến số điện thoại
  Future<Map<String, dynamic>> sendOTP({
    required String phoneNumber,
    required Function(String) onCodeSent,
    required Function(String) onError,
    required Function(PhoneAuthCredential) onVerificationCompleted,
  }) async {
    try {
      // Đảm bảo số điện thoại có định dạng quốc tế
      String formattedPhone = _formatPhoneNumber(phoneNumber);
      print('=== PHONE NUMBER FORMATTING ===');
      print('Original: $phoneNumber');
      print('Formatted: $formattedPhone');
      print('Is test number: ${_testPhoneNumbers.contains(formattedPhone)}');
      print('==============================');

      // Development mode - mock OTP sending
      if (_isDevelopmentMode) {
        _verificationId = 'mock_verification_id_${DateTime.now().millisecondsSinceEpoch}';
        await Future.delayed(const Duration(seconds: 1));
        onCodeSent('OTP đã được gửi đến $formattedPhone (Development Mode)');
        return {
          'success': true,
          'message': 'OTP sent in development mode',
          'phoneNumber': formattedPhone,
        };
      }

      // Check if this is a test phone number
      if (_testPhoneNumbers.contains(formattedPhone)) {
        print('Using test phone number: $formattedPhone');
        // For test numbers, Firebase handles everything automatically
        // We still need to call verifyPhoneNumber but Firebase will use test flow
      }

      await _auth.verifyPhoneNumber(
        phoneNumber: formattedPhone,
        verificationCompleted: (PhoneAuthCredential credential) async {
          print('=== VERIFICATION COMPLETED ===');
          print('Auto verification completed for test number');
          print('SMS Code: ${credential.smsCode}');
          print('Verification ID: ${credential.verificationId}');
          print('=============================');

          // For test numbers, Firebase might auto-complete
          _verificationId = credential.verificationId;
          onVerificationCompleted(credential);
        },
        verificationFailed: (FirebaseAuthException e) {
          String errorMessage = 'Xác thực thất bại';
          print('=== FIREBASE AUTH ERROR ===');
          print('Error Code: ${e.code}');
          print('Error Message: ${e.message}');
          print('Phone Number: $formattedPhone');
          print('Is Test Number: ${_testPhoneNumbers.contains(formattedPhone)}');
          print('Stack Trace: ${e.stackTrace}');
          print('========================');

          // Handle specific error codes
          switch (e.code) {
            case 'invalid-phone-number':
              errorMessage = 'Số điện thoại không hợp lệ. Vui lòng kiểm tra lại định dạng.';
              break;
            case 'too-many-requests':
              errorMessage = 'Quá nhiều yêu cầu từ thiết bị này.\n'
                  'Vui lòng:\n'
                  '• Chờ 15-30 phút rồi thử lại\n'
                  '• Hoặc sử dụng số điện thoại test: +84337252208\n'
                  '• Hoặc thử trên thiết bị khác';
              break;
            case 'quota-exceeded':
              errorMessage = 'Đã vượt quá giới hạn SMS hàng ngày. Vui lòng thử lại vào ngày mai.';
              break;
            case 'app-not-authorized':
              errorMessage = 'Ứng dụng chưa được cấu hình đúng cách.\nVui lòng kiểm tra SHA-1 fingerprint trong Firebase Console.';
              break;
            case 'network-request-failed':
              errorMessage = 'Lỗi kết nối mạng. Vui lòng kiểm tra internet và thử lại.';
              break;
            case 'captcha-check-failed':
              errorMessage = 'Xác thực reCAPTCHA thất bại. Vui lòng thử lại.';
              break;
            default:
              // Handle Play Integrity and other errors
              if (e.message?.contains('PlayIntegrity') == true ||
                  e.message?.contains('play-integrity') == true) {
                errorMessage = 'Lỗi xác thực thiết bị Play Integrity.\n'
                    'Vui lòng:\n'
                    '• Đảm bảo thiết bị có Google Play Services\n'
                    '• Thử trên thiết bị thật (không phải emulator)\n'
                    '• Kiểm tra SHA-1 fingerprint trong Firebase Console';
              } else {
                errorMessage = 'Lỗi xác thực: ${e.message ?? 'Không xác định'}';
              }
              break;
          }

          onError(errorMessage);
        },
        codeSent: (String verificationId, int? resendToken) {
          print('=== CODE SENT CALLBACK ===');
          print('Verification ID: $verificationId');
          print('Resend Token: $resendToken');
          print('Phone: $formattedPhone');
          print('========================');

          _verificationId = verificationId;
          _resendToken = resendToken;
          onCodeSent('OTP đã được gửi đến $formattedPhone');
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          _verificationId = verificationId;
        },
        timeout: const Duration(seconds: 60),
        forceResendingToken: _resendToken,
      );

      return {
        'success': true,
        'message': 'OTP đã được gửi',
        'phoneNumber': formattedPhone,
      };
    } catch (e) {
      return {
        'success': false,
        'error': 'Lỗi gửi OTP: ${e.toString()}',
      };
    }
  }

  // Xác thực OTP
  Future<Map<String, dynamic>> verifyOTP({
    required String otp,
  }) async {
    try {
      if (_verificationId == null) {
        return {
          'success': false,
          'error': 'Không tìm thấy mã xác thực. Vui lòng gửi lại OTP',
        };
      }

      // Development mode - mock OTP verification
      if (_isDevelopmentMode) {
        if (otp == '123456' || otp.length == 6) {
          return {
            'success': true,
            'user': null,
            'idToken': 'mock_id_token_${DateTime.now().millisecondsSinceEpoch}',
            'phoneNumber': '+84337252208',
          };
        } else {
          return {
            'success': false,
            'error': 'Mã OTP không đúng (Development: sử dụng 123456)',
          };
        }
      }

      // Special handling for verification ID issues
      if (_verificationId == null || _verificationId!.isEmpty) {
        print('Verification ID is null or empty. This might be a test number issue.');
        return {
          'success': false,
          'error': 'Lỗi xác thực: Không tìm thấy mã xác thực.\n'
              'Vui lòng:\n'
              '• Đảm bảo đã setup test phone number trong Firebase Console\n'
              '• Thử gửi lại OTP\n'
              '• Kiểm tra số điện thoại: +84337252208',
        };
      }

      print('=== OTP VERIFICATION DEBUG ===');
      print('Verification ID: $_verificationId');
      print('Verification ID length: ${_verificationId?.length}');
      print('Verification ID is empty: ${_verificationId?.isEmpty}');
      print('OTP Code: $otp');
      print('OTP Code length: ${otp.length}');
      print('Current timestamp: ${DateTime.now()}');
      print('=============================');

      PhoneAuthCredential credential = PhoneAuthProvider.credential(
        verificationId: _verificationId!,
        smsCode: otp,
      );

      print('Attempting to sign in with credential...');
      UserCredential userCredential = await _auth.signInWithCredential(credential);

      print('Sign in successful! Getting ID token...');
      // Lấy ID token để gửi lên backend
      String? idToken = await userCredential.user?.getIdToken();

      print('=== VERIFICATION SUCCESS ===');
      print('User UID: ${userCredential.user?.uid}');
      print('Phone Number: ${userCredential.user?.phoneNumber}');
      print('ID Token: ${idToken?.substring(0, 20)}...');
      print('===========================');

      return {
        'success': true,
        'user': userCredential.user,
        'idToken': idToken,
        'phoneNumber': userCredential.user?.phoneNumber,
      };
    } on FirebaseAuthException catch (e) {
      String errorMessage = 'Xác thực OTP thất bại';
      if (e.code == 'invalid-verification-code') {
        errorMessage = 'Mã OTP không đúng';
      } else if (e.code == 'session-expired') {
        errorMessage = 'Mã OTP đã hết hạn. Vui lòng gửi lại';
      }
      return {
        'success': false,
        'error': errorMessage,
      };
    } catch (e) {
      return {
        'success': false,
        'error': 'Lỗi xác thực: ${e.toString()}',
      };
    }
  }

  // Gửi lại OTP
  Future<Map<String, dynamic>> resendOTP({
    required String phoneNumber,
    required Function(String) onCodeSent,
    required Function(String) onError,
  }) async {
    return await sendOTP(
      phoneNumber: phoneNumber,
      onCodeSent: onCodeSent,
      onError: onError,
      onVerificationCompleted: (credential) {},
    );
  }



  // Đăng xuất Firebase Auth
  Future<void> signOut() async {
    await _auth.signOut();
    _verificationId = null;
    _resendToken = null;
  }

  // Lấy user hiện tại
  User? getCurrentUser() {
    return _auth.currentUser;
  }

  // Kiểm tra trạng thái đăng nhập
  bool isSignedIn() {
    return _auth.currentUser != null;
  }

  // Debug method để check verification ID
  Map<String, dynamic> getVerificationStatus() {
    return {
      'hasVerificationId': _verificationId != null,
      'verificationId': _verificationId,
      'verificationIdLength': _verificationId?.length,
      'resendToken': _resendToken,
      'isDevelopmentMode': _isDevelopmentMode,
    };
  }

  // Helper method để format số điện thoại
  String _formatPhoneNumber(String phoneNumber) {
    // Remove all spaces, dashes, and other non-digit characters except +
    String cleaned = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

    // If already has country code, return as is
    if (cleaned.startsWith('+')) {
      return cleaned;
    }

    // If starts with 84, add +
    if (cleaned.startsWith('84')) {
      return '+$cleaned';
    }

    // If starts with 0 (Vietnamese format), replace with +84
    if (cleaned.startsWith('0') && cleaned.length >= 10) {
      return '+84${cleaned.substring(1)}';
    }

    // Default: assume Vietnamese number and add +84
    return '+84$cleaned';
  }
}
