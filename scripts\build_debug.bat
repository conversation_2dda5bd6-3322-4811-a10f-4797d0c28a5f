@echo off
echo ========================================
echo Building BloodPlus for Development
echo ========================================

echo.
echo [1/4] Cleaning previous builds...
call flutter clean
if %errorlevel% neq 0 (
    echo ERROR: Flutter clean failed
    pause
    exit /b 1
)

echo.
echo [2/4] Getting dependencies...
call flutter pub get
if %errorlevel% neq 0 (
    echo ERROR: Flutter pub get failed
    pause
    exit /b 1
)

echo.
echo [3/4] Cleaning Android build...
cd android
call gradlew clean
if %errorlevel% neq 0 (
    echo ERROR: Gradle clean failed
    pause
    exit /b 1
)
cd ..

echo.
echo [4/4] Building debug APK...
call flutter build apk --debug
if %errorlevel% neq 0 (
    echo ERROR: Flutter build apk failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo Debug build completed successfully!
echo ========================================
echo.
echo File created: build\app\outputs\flutter-apk\app-debug.apk
echo.
echo This APK includes development features like mock OTP.
echo.
pause
